<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Epp\Services\EppDomainService;
use App\Modules\Notification\Services\NotificationService;
use App\Events\DomainHistoryEvent;
use Exception;

class DomainRequestDeleteJobService
{
    use UserLoggerTrait;

    private static ?self $instance = null;

    private function __construct()
    {
    }

    public static function instance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function processDeleteRequest(array $domain): void
    {
        try {
            // Step 1: Call EPP domain info to check if domain exists
            $eppInfoResponse = EppDomainService::instance()->callEppDomainInfo($domain['domainName']);
            $datastoreInfoResponse = EppDomainService::instance()->callDatastoreDomainInfo($domain['domainName']);

            // Log the responses for debugging
            app(AuthLogger::class)->info("EPP Info Response for {$domain['domainName']}: " . json_encode($eppInfoResponse));
            app(AuthLogger::class)->info("Datastore Info Response for {$domain['domainName']}: " . json_encode($datastoreInfoResponse));

            // Check if domain exists in EPP
            if ($this->isDomainNotFound($eppInfoResponse)) {
                app(AuthLogger::class)->error("Domain {$domain['domainName']} not found in EPP. Cannot proceed with clientDeleteProhibited removal.");
                $this->handleDomainNotFound($domain);
                return;
            }

            // Step 2: Call updateEppDomain to remove clientDeleteProhibited status
            $updatePayload = [
                'name' => $domain['domainName'],
                'statusRemove' => ['clientDeleteProhibited']
            ];

            app(AuthLogger::class)->info("Updating EPP domain {$domain['domainName']} with payload: " . json_encode($updatePayload));

            $updateResult = EppDomainService::instance()->updateEppDomain($updatePayload);

            // Log the update result
            app(AuthLogger::class)->info("EPP Update Result for {$domain['domainName']}: " . json_encode($updateResult));

            // Check if update was successful
            if ($this->isUpdateSuccessful($updateResult)) {
                app(AuthLogger::class)->info("Successfully removed clientDeleteProhibited status from domain {$domain['domainName']}");
                $this->handleSuccessfulUpdate($domain);
            } else {
                app(AuthLogger::class)->error("Failed to remove clientDeleteProhibited status from domain {$domain['domainName']}: " . json_encode($updateResult));
                $this->handleUpdateFailure($domain, $updateResult);
            }

        } catch (Exception $e) {
            app(AuthLogger::class)->error("Exception occurred while processing domain request delete for {$domain['domainName']}: " . $e->getMessage());
            $this->handleException($domain, $e);
            throw $e;
        }
    }

    private function isDomainNotFound(array $response): bool
    {
        return isset($response['status']) && $response['status'] === 'error' && 
               (isset($response['message']) && stripos($response['message'], 'no domain exist found') !== false);
    }

    private function isUpdateSuccessful(array $response): bool
    {
        return isset($response['status']) && $response['status'] === 'OK';
    }

    private function handleDomainNotFound(array $domain): void
    {
        // Send notification about domain not found
        // NotificationService::sendDomainNotFoundNotification($domain['domainName']);
        
        // Log domain history
        $this->logDomainHistory($domain, 'DOMAIN_UPDATED', 'failed', 'Domain not found in EPP registry - cannot remove clientDeleteProhibited status');
    }

    private function handleSuccessfulUpdate(array $domain): void
    {
        // Log successful update
        $this->logDomainHistory($domain, 'DOMAIN_UPDATED', 'success', 'Successfully removed clientDeleteProhibited status from domain');
        
        // TODO: Add any additional success handling if needed
    }

    private function handleUpdateFailure(array $domain, array $updateResult): void
    {
        // Send notification about update failure
        // NotificationService::sendUpdateFailureNotification($domain['domainName'], $updateResult);
        
        // Log domain history
        $errorMessage = isset($updateResult['message']) ? $updateResult['message'] : 'Unknown error';
        $this->logDomainHistory($domain, 'DOMAIN_UPDATED', 'failed', "Failed to remove clientDeleteProhibited status: {$errorMessage}");
    }

    private function handleException(array $domain, Exception $e): void
    {
        // Log domain history for exception
        $this->logDomainHistory($domain, 'DOMAIN_UPDATED', 'failed', "Exception occurred during clientDeleteProhibited removal: " . $e->getMessage());
    }

    private function logDomainHistory(array $domain, string $type, string $status, string $message): void
    {
        event(new DomainHistoryEvent([
            'domain_id' => $domain['domainId'],
            'type' => $type,
            'status' => $status,
            'user_id' => $domain['userId'] ?? null,
            'message' => $message,
            'payload' => json_encode($domain),
        ]));
    }
}
