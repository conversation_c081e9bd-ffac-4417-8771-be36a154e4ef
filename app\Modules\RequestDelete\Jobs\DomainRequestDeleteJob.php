<?php

namespace App\Modules\RequestDelete\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\RequestDelete\Services\DomainRequestDeleteJobService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueTypes;
use App\Util\Helper\DomainParser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Exception;
use Throwable;

class DomainRequestDeleteJob implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UserLoggerTrait;

    private $params;

    /**
     * if process takes longer than indicated timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct(
        string $domainId,
        string $domainName,
        string $userId,
        string $userEmail,
        string $reason,
        string $createdDate,
        string $supportNote = null,
        int $adminId = null,
        string $adminName = null,
        string $adminEmail = null
    ) {
        $registry = DomainParser::getRegistryName($domainName);

        $this->params = [
            'domainId' => $domainId,
            'domainName' => $domainName,
            'userId' => $userId,
            'userEmail' => $userEmail,
            'reason' => $reason,
            'createdDate' => $createdDate,
            'supportNote' => $supportNote,
            'adminId' => $adminId,
            'adminName' => $adminName,
            'adminEmail' => $adminEmail
        ];

        $this->onConnection(QueueConnection::DOMAIN_REQUEST_DELETE);
        $this->onQueue(QueueTypes::DOMAIN_REQUEST_DELETE[$registry]);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return (int) $this->params['domainId'];
    }

    public function handle(): void
    {
        try {
            app(AuthLogger::class)->info("Starting domain request delete job for domain: {$this->params['domainName']} (ID: {$this->params['domainId']})");
            DomainRequestDeleteJobService::instance()->processDeleteRequest($this->params);
            app(AuthLogger::class)->info("Successfully completed domain request delete job for domain: {$this->params['domainName']}");
        } catch (Exception $e) {
            app(AuthLogger::class)->error("Domain request delete job failed for domain: {$this->params['domainName']} (ID: {$this->params['domainId']}). Error: " . $e->getMessage());
            app(AuthLogger::class)->error("Job parameters: " . json_encode($this->params));
            app(AuthLogger::class)->info('number of attempts: ' . $this->attempts());
            throw $e;
        }
    }

    public function backoff(): array
    {
        return [5, 10];
    }

    public function failed(?Throwable $exception): void
    {
        app(AuthLogger::class)->error("Domain request delete job failed permanently for domain: {$this->params['domainName']} (ID: {$this->params['domainId']}). Error: " . $exception->getMessage());
        // TODO: Add notification service for failed job
    }
}
