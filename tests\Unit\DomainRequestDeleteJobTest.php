<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Modules\RequestDelete\Jobs\DomainRequestDeleteJob;
use App\Modules\RequestDelete\Services\DomainRequestDeleteJobService;
use App\Modules\Epp\Services\EppDomainService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class DomainRequestDeleteJobTest extends TestCase
{
    use RefreshDatabase;

    public function test_job_can_be_instantiated()
    {
        $job = new DomainRequestDeleteJob(
            '1',
            'example.com',
            '1',
            '<EMAIL>',
            'Test deletion',
            now()->toDateTimeString(),
            'Test support note',
            1,
            'Admin User',
            '<EMAIL>'
        );

        $this->assertInstanceOf(DomainRequestDeleteJob::class, $job);
    }

    public function test_job_service_can_be_instantiated()
    {
        $service = DomainRequestDeleteJobService::instance();
        $this->assertInstanceOf(DomainRequestDeleteJobService::class, $service);
    }

    public function test_job_service_singleton_pattern()
    {
        $service1 = DomainRequestDeleteJobService::instance();
        $service2 = DomainRequestDeleteJobService::instance();
        
        $this->assertSame($service1, $service2);
    }

    public function test_job_unique_id_generation()
    {
        $job = new DomainRequestDeleteJob(
            '123',
            'example.com',
            '1',
            '<EMAIL>',
            'Test deletion',
            now()->toDateTimeString()
        );

        $this->assertEquals(123, $job->uniqueId());
    }
}
