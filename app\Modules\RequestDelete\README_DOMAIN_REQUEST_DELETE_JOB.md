# Domain Request Delete Job

This document describes the `DomainRequestDeleteJob` that handles the removal of `clientDeleteProhibited` status from domains before they can be deleted.

## Overview

The `DomainRequestDeleteJob` is a queued job that:
1. Calls `callEppDomainInfo` to check if the domain exists in the EPP registry
2. Calls `callDatastoreDomainInfo` to check domain information in the datastore
3. Calls `updateEppDomain` with payload containing domain name and `statusRemove: clientDeleteProhibited`

## Files Created

### Job Class
- `app/Modules/RequestDelete/Jobs/DomainRequestDeleteJob.php`
  - Main job class that implements `ShouldBeUnique` and `ShouldQueue`
  - Handles job execution and error handling
  - Uses registry-specific queues (VERISIGN-REQUEST-DELETE, PIR-REQUEST-DELETE)

### Service Classes
- `app/Modules/RequestDelete/Services/DomainRequestDeleteJobService.php`
  - Contains the main business logic for processing the delete request
  - Handles EPP domain info calls and domain updates
  - Manages error handling and logging

- `app/Modules/RequestDelete/Services/DomainRequestDeleteJobDispatchService.php`
  - Service for dispatching the job with proper configuration
  - Follows the existing pattern used in other job dispatch services

### Configuration Updates
- `app/Util/Constant/QueueConnection.php` - Added `DOMAIN_REQUEST_DELETE` constant
- `app/Util/Constant/QueueTypes.php` - Added queue types for Verisign and PIR registries
- `config/queue.php` - Added `domain_request_delete_jobs` connection configuration
- `supervisor.conf` - Added supervisor configuration for the new job worker
- `app/Modules/Job/Constants/JobConnection.php` - Added job connection mapping

## Usage

### Dispatching the Job

```php
use App\Modules\RequestDelete\Jobs\DomainRequestDeleteJob;

// Direct dispatch
DomainRequestDeleteJob::dispatch(
    $domainId,
    $domainName,
    $userId,
    $userEmail,
    $reason,
    $createdDate,
    $supportNote,
    $adminId,
    $adminName,
    $adminEmail
);

// Using the dispatch service
use App\Modules\RequestDelete\Services\DomainRequestDeleteJobDispatchService;

DomainRequestDeleteJobDispatchService::instance()->dispatch([
    'domainId' => $domainId,
    'domainName' => $domainName,
    'userId' => $userId,
    'userEmail' => $userEmail,
    'reason' => $reason,
    'createdDate' => $createdDate,
    'supportNote' => $supportNote,
    'adminId' => $adminId,
    'adminName' => $adminName,
    'adminEmail' => $adminEmail
]);
```

### Integration with DomainDeleteService

The job is automatically dispatched from `DomainDeleteService::processApprovedRequest()` method before the actual domain cancellation job.

## Queue Configuration

### Database Setup
```bash
# Create the queue table
php artisan queue:table --connection=domain_request_delete_jobs
php artisan migrate --database=domain_request_delete_jobs
```

### Worker Commands
```bash
# Run the worker
php artisan queue:work domain_request_delete_jobs --queue=VERISIGN-REQUEST-DELETE,PIR-REQUEST-DELETE --tries=3 --timeout=120 --max-time=900 --stop-when-empty
```

### Supervisor Configuration
The supervisor configuration is already added to `supervisor.conf` as `admin-domain-request-delete`.

## Error Handling

The job includes comprehensive error handling:
- Domain not found in EPP registry
- Failed EPP domain updates
- Exception handling with proper logging
- Domain history logging for all scenarios

## Logging

All operations are logged using `AuthLogger` with detailed information about:
- EPP info responses
- Datastore info responses  
- Update results
- Error conditions
- Job execution status

## Testing

Basic unit tests are provided in `tests/Unit/DomainRequestDeleteJobTest.php` to verify:
- Job instantiation
- Service singleton pattern
- Unique ID generation
- Basic functionality

## Queue Monitoring

The job can be monitored through the existing job monitoring system using the job connection `admin_domain_request_delete_jobs`.
