[2025-09-03 07:01:59] local.INFO: Starting domain request delete job for domain: polakasxz.net (ID: 145)  
[2025-09-03 07:02:03] local.INFO: EPP Info Response for polakasxz.net: {"data":{"name":"polakasxz.net","registrant":"mj11755500358","registryDomainId":"140474737_DOMAIN_NET-VRSN","expiry":"2026-09-03T03:12:18Z","updated":"2025-09-03T03:15:19Z","created":"2025-09-03T03:12:18Z","domainPrivacyProtection":true,"contacts":{"tech":"mj11755500358","admin":"mj11755500358","billing":"mj11755500358"},"status":["clientDeleteProhibited","clientTransferProhibited","inactive"],"hosts":[],"nameservers":[],"extensions":[{"rgpExtStatus":["addPeriod"]}]},"message":"showing info of polakasxz.net","status":"OK","statusCode":200}  
[2025-09-03 07:02:03] local.INFO: Datastore Info Response for polakasxz.net: {"data":{"name":"polakasxz.net","registrant":"mj11755500358","registryDomainId":"140474737_DOMAIN_NET-VRSN","expiry":"2026-09-03T03:12:18Z","updated":"2025-09-03T03:15:20Z","created":"2025-09-03T03:12:18Z","domainPrivacyProtection":true,"contacts":{"tech":"mj11755500358","admin":"mj11755500358","billing":"mj11755500358"},"status":["clientTransferProhibited","clientDeleteProhibited","inactive"],"hosts":[],"nameservers":[],"extensions":[]},"message":"showing info from database of polakasxz.net","status":"OK","statusCode":200}  
[2025-09-03 07:02:03] local.INFO: Updating EPP domain polakasxz.net with payload: {"name":"polakasxz.net","statusRemove":"clientDeleteProhibited"}  
[2025-09-03 07:02:03] local.ERROR: HTTP request returned status code 400:
{"message":"JSON parse error: Cannot construct instance of `java.util.ArrayList` (although at least one Creator exists): (truncated...)
 ; {"message":"JSON parse error: Cannot construct instance of `java.util.ArrayList` (although at least one Creator exists): no String-argument constructor\/factory method to deserialize from String value ('clientDeleteProhibited')","status":"BAD_REQUEST","statusCode":400}  
[2025-09-03 07:02:03] local.INFO: EPP Update Result for polakasxz.net: {"status":{"message":"JSON parse error: Cannot construct instance of `java.util.ArrayList` (although at least one Creator exists): no String-argument constructor\/factory method to deserialize from String value ('clientDeleteProhibited')","status":"BAD_REQUEST","statusCode":400},"errors":"error"}  
[2025-09-03 07:02:03] local.ERROR: Failed to remove clientDeleteProhibited status from domain polakasxz.net: {"status":{"message":"JSON parse error: Cannot construct instance of `java.util.ArrayList` (although at least one Creator exists): no String-argument constructor\/factory method to deserialize from String value ('clientDeleteProhibited')","status":"BAD_REQUEST","statusCode":400},"errors":"error"}  
[2025-09-03 07:02:03] local.INFO: Domain History: Failed to remove clientDeleteProhibited status: Unknown error  
[2025-09-03 07:02:03] local.INFO: Successfully completed domain request delete job for domain: polakasxz.net  
